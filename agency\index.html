<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthetix | Digital presence, authentically crafted</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a1a1a',
                        secondary: '#3a3a3a',
                        accent: '#00a8e8',
                        dark: '#121212',
                        light: '#f5f5f5'
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    <style type="text/css">

        
        .hero-bg {
            background: radial-gradient(circle at 10% 20%, rgba(14, 116, 144, 0.08) 0%, rgba(255, 255, 255, 0) 20%),
                        radial-gradient(circle at 90% 30%, rgba(13, 148, 136, 0.08) 0%, rgba(255, 255, 255, 0) 25%);
        }
        
        .section-pattern {
            background: radial-gradient(circle at 15% 50%, rgba(13, 148, 136, 0.03) 0%, rgba(255, 255, 255, 0) 25%);
        }
        
        .service-card, .portfolio-item, .feature-card {
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94), 
                        box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        .service-card:hover, .portfolio-item:hover, .feature-card:hover {
            transform: translateY(-5px) scale(1.01);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* Enhanced Portfolio Item Effects */
        .portfolio-item {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
            cursor: pointer;
        }

        .portfolio-item::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(13, 148, 136, 0.6) 100%);
            opacity: 0;
            transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
            border-radius: 12px;
            z-index: 1;
        }

        .portfolio-item::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
            z-index: 2;
        }

        .portfolio-item:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .portfolio-item:hover::before {
            opacity: 1;
        }

        .portfolio-item:hover::after {
            width: 100px;
            height: 100px;
            opacity: 0;
        }

        .portfolio-item:hover .portfolio-content {
            opacity: 1;
            transform: translateY(0);
        }

        .portfolio-item .portfolio-content {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
            z-index: 3;
        }

        .portfolio-item img {
            transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .portfolio-item:hover img {
            transform: scale(1.1);
        }

        /* Portfolio title animation */
        .portfolio-title {
            position: relative;
            overflow: hidden;
        }

        .portfolio-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00a8e8, transparent);
            transition: left 0.5s ease;
        }

        .portfolio-item:hover .portfolio-title::after {
            left: 100%;
        }
        
        /* Enhanced Parallax Effects */
        .parallax {
            position: relative;
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            overflow: hidden;
        }

        .parallax-bg {
            position: absolute;
            top: -20%;
            left: 0;
            width: 100%;
            height: 120%;
            will-change: transform;
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            transition: transform 0.1s ease-out;
        }

        /* Subtle parallax for section backgrounds */
        .section-parallax {
            position: relative;
            overflow: hidden;
        }

        .section-parallax::before {
            content: '';
            position: absolute;
            top: -10%;
            left: 0;
            width: 100%;
            height: 110%;
            background: inherit;
            will-change: transform;
            z-index: -1;
        }

        /* Floating elements with parallax */
        .parallax-float {
            position: absolute;
            will-change: transform;
            transition: transform 0.1s ease-out;
        }

        @media (max-width: 768px) {
            .parallax, .parallax-bg {
                background-attachment: scroll;
            }
            .parallax-bg {
                top: 0;
                height: 100%;
            }
        }
        
        .btn-hover {
            position: relative;
            overflow: hidden;
        }
        
        .btn-hover::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        
        .btn-hover:hover::after {
            transform: translateX(0);
        }
        
        /* Enhanced Animation Styles */
        .animate-on-scroll {
            opacity: 1;
            transform: none;
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }

        /* Animation classes for when elements are out of view */
        .animate-on-scroll.animate-out {
            opacity: 0;
        }
        .animate-on-scroll.animate-out[data-direction="left"] {
            transform: translateX(-50px);
        }
        .animate-on-scroll.animate-out[data-direction="right"] {
            transform: translateX(50px);
        }
        .animate-on-scroll.animate-out[data-direction="up"] {
            transform: translateY(50px);
        }
        .animate-on-scroll.animate-out[data-direction="down"] {
            transform: translateY(-50px);
        }
        .animate-on-scroll.animate-out[data-direction="fade"] {
            transform: scale(0.9);
        }
        .animate-on-scroll.animate-out[data-direction="scale"] {
            transform: scale(0.8);
        }

        /* Enhanced Delay classes */
        .animate-on-scroll[data-delay="0"] {
            transition-delay: 0s;
        }
        .animate-on-scroll[data-delay="0.1"] {
            transition-delay: 0.1s;
        }
        .animate-on-scroll[data-delay="0.2"] {
            transition-delay: 0.2s;
        }
        .animate-on-scroll[data-delay="0.3"] {
            transition-delay: 0.3s;
        }
        .animate-on-scroll[data-delay="0.4"] {
            transition-delay: 0.4s;
        }
        .animate-on-scroll[data-delay="0.5"] {
            transition-delay: 0.5s;
        }
        .animate-on-scroll[data-delay="0.6"] {
            transition-delay: 0.6s;
        }
        .animate-on-scroll[data-delay="0.7"] {
            transition-delay: 0.7s;
        }
        .animate-on-scroll[data-delay="0.8"] {
            transition-delay: 0.8s;
        }
        .animate-on-scroll[data-delay="0.9"] {
            transition-delay: 0.9s;
        }
        .animate-on-scroll[data-delay="1.0"] {
            transition-delay: 1.0s;
        }
        .animate-on-scroll[data-delay="1.1"] {
            transition-delay: 1.1s;
        }

        /* Enhanced Typing animation for hero headline */
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: #1a1a1a }
        }

        .typing-animation {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            border-right: 3px solid #1a1a1a;
            animation: typing 2s steps(25, end), blink-caret 1s step-end infinite;
            animation-delay: 0.5s;
            animation-fill-mode: both;
        }

        /* Hero headline stagger animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-headline-1 {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .hero-headline-2 {
            animation: fadeInUp 0.8s ease-out 0.3s;
            animation-fill-mode: both;
        }

        .hero-subtitle {
            animation: fadeInUp 0.8s ease-out 0.6s;
            animation-fill-mode: both;
        }

        /* Enhanced Button hover effects */
        .btn-hover {
            position: relative;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow: hidden;
        }

        .btn-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-hover:hover::before {
            left: 100%;
        }

        .btn-hover:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 15px 35px rgba(13, 148, 136, 0.3);
        }

        .btn-hover:active {
            transform: translateY(0) scale(0.98);
            box-shadow: 0 5px 15px rgba(13, 148, 136, 0.2);
            transition: all 0.1s ease;
        }

        /* Secondary button hover effects */
        .btn-secondary {
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .btn-secondary::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: #1a1a1a;
            transition: width 0.4s ease;
            z-index: -1;
        }

        .btn-secondary:hover::after {
            width: 100%;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(26, 26, 26, 0.2);
        }

        /* Hero image reveal animation */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .hero-image {
            animation: slideInRight 1s ease-out 0.8s;
            animation-fill-mode: both;
        }

        /* Floating background elements */
        @keyframes float-slow {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        @keyframes float-medium {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(-3deg); }
        }

        .float-slow {
            animation: float-slow 6s ease-in-out infinite;
        }

        .float-medium {
            animation: float-medium 4s ease-in-out infinite;
        }

        /* Icon Animation Effects */
        @keyframes iconBounce {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-5px) scale(1.05); }
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(26, 26, 26, 0.3); }
            50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(26, 26, 26, 0); }
        }

        @keyframes iconRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .icon-container {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .icon-container::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(26, 26, 26, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .icon-container:hover::before {
            width: 100%;
            height: 100%;
        }

        .icon-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(26, 26, 26, 0.15);
        }

        .icon-bounce:hover .fas {
            animation: iconBounce 0.6s ease-in-out;
        }

        .icon-pulse:hover {
            animation: iconPulse 1s ease-in-out;
        }

        .icon-rotate:hover .fas {
            animation: iconRotate 0.5s ease-in-out;
        }

        /* Feature Card Enhancements */
        .feature-card {
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(26, 26, 26, 0.03), transparent);
            transition: left 0.5s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover .icon-container {
            transform: translateY(-5px) scale(1.1);
        }

        /* Text Highlight Animation */
        .highlight-on-hover {
            position: relative;
            transition: all 0.3s ease;
        }

        .highlight-on-hover::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #1a1a1a, #3a3a3a);
            transition: width 0.3s ease;
        }

        .highlight-on-hover:hover::after {
            width: 100%;
        }

        .highlight-on-hover:hover {
            color: #1a1a1a;
        }

        /* Enhanced Form Field Focus Effects */
        .form-field {
            position: relative;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .form-field::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 2px solid transparent;
            border-radius: 8px;
            background: linear-gradient(45deg, #1a1a1a, #00a8e8) border-box;
            -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: exclude;
            mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .form-field:focus-within::before {
            opacity: 1;
        }

        .form-field:focus-within {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(26, 26, 26, 0.1);
        }

        .form-field input:focus,
        .form-field textarea:focus {
            border-color: #1a1a1a;
            box-shadow: 0 0 0 3px rgba(26, 26, 26, 0.1);
            transform: scale(1.01);
        }

        .form-field label {
            transition: all 0.3s ease;
        }

        .form-field:focus-within label {
            color: #1a1a1a;
            transform: translateY(-2px);
        }

        /* Enhanced Footer Social Icons */
        .social-icon {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            overflow: hidden;
        }

        .social-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a1a, #00a8e8);
            border-radius: 50%;
            transform: scale(0);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: -1;
        }

        .social-icon:hover::before {
            transform: scale(1);
        }

        .social-icon:hover {
            transform: translateY(-3px) rotate(5deg) scale(1.1);
            color: white;
            box-shadow: 0 10px 20px rgba(26, 26, 26, 0.2);
        }

        .social-icon:nth-child(2):hover {
            transform: translateY(-3px) rotate(-5deg) scale(1.1);
        }

        .social-icon:nth-child(3):hover {
            transform: translateY(-3px) rotate(8deg) scale(1.1);
        }

        .social-icon:nth-child(4):hover {
            transform: translateY(-3px) rotate(-8deg) scale(1.1);
        }

        /* Newsletter form enhancement */
        .newsletter-form {
            position: relative;
            overflow: hidden;
        }

        .newsletter-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
        }

        .newsletter-form:focus-within::before {
            left: 100%;
        }

        .newsletter-form input:focus {
            background: rgba(255, 255, 255, 0.1);
            border-color: #00a8e8;
        }
        
        @media (max-width: 768px) {
            .parallax {
                background-attachment: scroll;
            }
            body {
                font-size: 16px;
            }
            h1 {
                font-size: 2.5rem;
            }
            h2 {
                font-size: 1.8rem;
            }
            h3 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 2rem;
            }
            h2 {
                font-size: 1.5rem;
            }
            h3 {
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-dark">
    <!-- Navigation -->
    <nav class="fixed w-full bg-white shadow-sm z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20 items-center">
                <div class="flex items-center">
                    <a href="#" class="flex items-center">
                        <div class="bg-primary w-10 h-10 rounded-full flex items-center justify-center">
                            <svg viewBox="0 0 24 24" class="w-6 h-6 text-white">
                                <path fill="currentColor" d="M12 2L3 7l9 5 9-5-9-5zm0 14L3 11l9 5 9-5-9-5z"/>
                                <path fill="currentColor" d="M3 11l9 5 9-5" opacity="0.5"/>
                            </svg>
                        </div>
                        <span class="ml-3 text-xl font-bold text-primary">Synthetix</span>
                    </a>
                </div>
                
                <div class="hidden md:flex items-center space-x-10">
                    <a href="#hero" class="text-dark hover:text-primary transition-colors duration-300">Home</a>
                    <a href="#services" class="text-dark hover:text-primary relative transition-colors duration-300">
                        <span class="relative">
                            Services
                            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </span>
                    </a>
                    <a href="#portfolio" class="text-dark hover:text-primary transition-colors duration-300">Portfolio</a>
                    <a href="#about" class="text-dark hover:text-primary transition-colors duration-300">About</a>
                    <a href="#contact" class="text-dark hover:text-primary transition-colors duration-300">Contact</a>
                    <a href="#contact" class="bg-primary hover:bg-secondary text-white px-6 py-2 rounded-full transition-colors duration-300 font-medium">
                        Get Started
                    </a>
                </div>
                
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-dark hover:text-primary">
                        <i class="fas fa-bars text-2xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden fixed inset-0 bg-white z-40">
        <div class="flex justify-end p-6">
            <button id="mobile-close-button" class="text-dark hover:text-primary">
                <i class="fas fa-times text-3xl"></i>
            </button>
        </div>
        <div class="flex flex-col items-center space-y-8">
            <a href="#hero" class="text-2xl text-dark hover:text-primary transition-colors duration-300">Home</a>
            <a href="#services" class="text-2xl text-dark hover:text-primary transition-colors duration-300">Services</a>
            <a href="#portfolio" class="text-2xl text-dark hover:text-primary transition-colors duration-300">Portfolio</a>
            <a href="#about" class="text-2xl text-dark hover:text-primary transition-colors duration-300">About</a>
            <a href="#contact" class="text-2xl text-dark hover:text-primary transition-colors duration-300">Contact</a>
            <a href="#contact" class="bg-primary hover:bg-secondary text-white px-8 py-3 rounded-full text-xl transition-colors duration-300 font-medium mt-6">
                Get Started
            </a>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="hero" class="min-h-screen pt-24 pb-16 md:pt-36 md:pb-24 flex items-center relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSg0NSkiPjxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzAwMCIgZmlsbC1vcGFjaXR5PSIwLjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=')]"></div>
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                        <span class="block hero-headline-1">Digital presence,</span>
                        <span class="text-primary hero-headline-2 typing-animation">authentically crafted</span>
                        <span class="block hero-subtitle text-gray-600 text-lg mt-4">AI-powered, human-perfected</span>
                    </h1>
                    <p class="text-lg text-[#666666] mb-10 max-w-2xl animate-on-scroll" data-delay="0.9">
                        Your business deserves more than just a website - get an AI-powered digital storefront that works 24/7 to attract customers and grow your brand.
                    </p>
                    <div class="flex flex-wrap gap-4 animate-on-scroll" data-delay="1.1">
                        <a href="#services" class="bg-primary hover:bg-secondary text-white px-8 py-4 rounded-full font-medium btn-hover">
                            See What AI Can Do For You
                        </a>
                        <a href="#portfolio" class="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-full font-medium btn-secondary">
                            View Success Stories
                        </a>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="relative">
                        <div class="absolute -top-6 -right-6 w-64 h-64 bg-accent rounded-full opacity-20 blur-3xl z-0 float-slow"></div>
                        <div class="absolute bottom-10 -left-10 w-48 h-48 bg-primary rounded-full opacity-20 blur-3xl z-0 float-medium"></div>
                        <div class="relative bg-gradient-to-br from-white to-light shadow-xl rounded-2xl p-6 z-10 overflow-hidden">
                            <div class="rounded-xl overflow-hidden border-4 border-white shadow-lg mb-6">
                                <div class="bg-gray-200 border-b-8 border-dark h-8 flex items-center px-4">
                                    <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                </div>
                                <div class="relative">
                                    <img src="https://images.unsplash.com/photo-1551836022-d5d88e9218df?auto=format&fit=crop&q=80&w=800" alt="AI generated website" class="w-full h-auto">
                                    <div class="absolute bottom-0 left-0 p-4 w-full bg-gradient-to-t from-dark/90 to-transparent">
                                        <div class="text-white font-bold text-xl">Modern AI-Generated Website</div>
                                        <div class="text-primary text-sm">For Cafés & Restaurants</div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2 justify-center">
                                <div class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Custom Design</div>
                                <div class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">AI-Powered</div>
                                <div class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">SEO Optimized</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem/Solution Section -->
    <section id="problem" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-2xl text-primary font-bold mb-2 animate-on-scroll" data-delay="0" data-direction="up">
                    Your Digital First Impression
                </h2>
                <h3 class="text-4xl font-bold mb-6 animate-on-scroll" data-delay="0.1" data-direction="up">
                    AI Solves What's Broken in Web Design
                </h3>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-delay="0.2" data-direction="fade">
                    94% of first impressions are design-related. Don't let a dated website turn customers away - our AI-powered solutions fix what's broken in traditional web design.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div class="animate-on-scroll" data-delay="0.3" data-direction="left">
                    <div class="grid grid-cols-1 gap-8">
                        <div class="flex items-start">
                            <div class="bg-primary/10 p-4 rounded-xl mr-6 group-hover:bg-primary/20 transition-colors duration-300">
                                <i class="fas fa-ban text-3xl text-primary group-hover:animate-pulse"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">Your Website is Losing You Money</h4>
                                <p class="text-gray-600">Every day with an outdated website means missed customers and revenue. Visitors judge your business in 0.05 seconds - make sure they see your best.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-primary/10 p-4 rounded-xl mr-6">
                                <i class="fas fa-clock text-3xl text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">Weeks of Waiting, Thousands Wasted</h4>
                                <p class="text-gray-600">Traditional agencies take 6-12 weeks and charge $5,000+. Our AI-powered process delivers better results in days, not months.</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-primary/10 p-4 rounded-xl mr-6">
                                <i class="fas fa-search text-3xl text-primary"></i>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2">Buried on Page 4 of Google</h4>
                                <p class="text-gray-600">75% of users never scroll past the first page. Our AI-optimized sites are built to rank from day one, putting you where customers can find you.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="animate-on-scroll" data-delay="0.4" data-direction="right">
                    <div class="bg-gradient-to-br from-light to-white shadow-xl rounded-2xl overflow-hidden border border-gray-100">
                        <div class="p-8">
                            <h3 class="text-2xl font-bold mb-4 text-primary">Our AI Solution</h3>
                            <p class="text-gray-600 mb-6">
                                AI Web Solutions Agency harnesses cutting-edge artificial intelligence to solve these challenges: 
                            </p>
                            <ul class="space-y-4">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>AI-generated designs tailored to your business aesthetic</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Automatically optimized for search engines</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Mobile-responsive layouts that look perfect on any device</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Fast development at a fraction of traditional costs</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Ongoing maintenance and SEO optimization</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 section-pattern">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-xl text-primary font-semibold mb-2 animate-on-scroll" data-delay="0" data-direction="up">
                    What We Offer
                </h2>
                <h3 class="text-3xl md:text-4xl font-bold mb-6 animate-on-scroll highlight-on-hover" data-delay="0.1" data-direction="up">
                    Precision-Crafted Digital Solutions
                </h3>
                <p class="text-lg text-[#666666] max-w-3xl mx-auto animate-on-scroll" data-delay="0.2" data-direction="fade">
                    From concept to launch and beyond, our AI-driven process delivers exceptional websites efficiently.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Service Card 1 -->
                <div class="animate-on-scroll" data-delay="0.3" data-direction="up">
                    <div class="bg-white service-card feature-card h-full rounded-2xl shadow-lg overflow-hidden transition-all duration-300">
                        <div class="p-8">
                            <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center mb-6 icon-container icon-pulse">
                                <i class="fas fa-lightbulb text-3xl text-primary"></i>
                            </div>
                            <h4 class="text-xl md:text-2xl font-bold mb-4 highlight-on-hover">AI-Generated Design</h4>
                            <p class="text-[#666666] mb-6">
                                We use cutting-edge AI to create beautiful design concepts tailored to your brand and industry.
                            </p>
                            <ul class="space-y-3 mb-8">
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Multiple design options created instantly</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Customizable to match your unique vision</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Professional color schemes and typography</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Service Card 2 -->
                <div class="animate-on-scroll" data-delay="0.4" data-direction="up">
                    <div class="bg-white service-card feature-card h-full rounded-2xl shadow-lg overflow-hidden transition-all duration-300">
                        <div class="p-8">
                            <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center mb-6 icon-container icon-bounce">
                                <i class="fas fa-code text-3xl text-primary"></i>
                            </div>
                            <h4 class="text-2xl font-bold mb-4 highlight-on-hover">Intelligent Development</h4>
                            <p class="text-gray-600 mb-6">
                                Our AI-powered development platform creates clean, efficient code that powers your website.
                            </p>
                            <ul class="space-y-3 mb-8">
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Clean, semantic HTML/CSS/JavaScript</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Automated responsive design for all devices</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Built-in optimization for speed and performance</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Service Card 3 -->
                <div class="animate-on-scroll" data-delay="0.5" data-direction="up">
                    <div class="bg-white service-card feature-card h-full rounded-2xl shadow-lg overflow-hidden transition-all duration-300">
                        <div class="p-8">
                            <div class="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center mb-6 icon-container icon-rotate">
                                <i class="fas fa-search text-3xl text-primary"></i>
                            </div>
                            <h4 class="text-2xl font-bold mb-4 highlight-on-hover">Ongoing SEO & Support</h4>
                            <p class="text-gray-600 mb-6">
                                Your success continues after launch with our comprehensive maintenance and optimization services.
                            </p>
                            <ul class="space-y-3 mb-8">
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Continuous SEO optimization</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Quick response to minor changes</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-circle text-xs text-primary mt-2 mr-3"></i>
                                    <span>Priority handling of major issues</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us -->
    <section class="py-20 bg-gradient-to-br from-white to-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="animate-on-scroll" data-delay="0">
                    <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&q=80&w=800" alt="Why choose us" class="rounded-2xl shadow-xl w-full">
                </div>
                
                <div>
                    <h2 class="text-2xl text-primary font-bold mb-2 animate-on-scroll" data-delay="0.1">
                        Why Choose Our Agency
                    </h2>
                    <h3 class="text-4xl font-bold mb-8 animate-on-scroll" data-delay="0.2">
                        The Perfect Blend of AI & Human Expertise
                    </h3>
                    
                    <div class="space-y-8">
                        <div class="flex animate-on-scroll" data-delay="0.3" data-direction="left">
                            <div class="mr-6">
                                <div class="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center icon-container icon-bounce">
                                    <i class="fas fa-bolt text-2xl text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2 highlight-on-hover">From Concept to Customers in Days</h4>
                                <p class="text-gray-600">While others take months, our AI-powered process delivers your stunning website in as little as 72 hours.</p>
                            </div>
                        </div>

                        <div class="flex animate-on-scroll" data-delay="0.4" data-direction="left">
                            <div class="mr-6">
                                <div class="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center icon-container icon-pulse">
                                    <i class="fas fa-shield-alt text-2xl text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2 highlight-on-hover">Handcrafted Perfection</h4>
                                <p class="text-gray-600">AI creates the foundation, then I personally refine every detail until it's perfect - your satisfaction guaranteed.</p>
                            </div>
                        </div>

                        <div class="flex animate-on-scroll" data-delay="0.5" data-direction="left">
                            <div class="mr-6">
                                <div class="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center icon-container icon-rotate">
                                    <i class="fas fa-cogs text-2xl text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-xl font-bold mb-2 highlight-on-hover">Always Evolving, Always Improving</h4>
                                <p class="text-gray-600">Your website grows with your business - continuous updates ensure you always have the latest advantages.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section id="portfolio" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-2xl text-primary font-bold mb-2 animate-on-scroll" data-delay="0" data-direction="up">
                    Our Work
                </h2>
                <h3 class="text-4xl font-bold mb-6 animate-on-scroll highlight-on-hover" data-delay="0.1" data-direction="up">
                    Portfolio Highlights
                </h3>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto animate-on-scroll" data-delay="0.2" data-direction="fade">
                    Explore our custom AI-powered websites created for businesses like yours.
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Portfolio Item 1 -->
                <div class="animate-on-scroll" data-delay="0.3" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1514933651103-005eec06c04b?auto=format&fit=crop&q=80&w=800" alt="Coffee Shop Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Urban Roast Café</h4>
                            <p class="text-accent font-medium">Coffee Shop Website</p>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Item 2 -->
                <div class="animate-on-scroll" data-delay="0.4" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?auto=format&fit=crop&q=80&w=800" alt="Restaurant Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Bella Trattoria</h4>
                            <p class="text-accent font-medium">Restaurant Website</p>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Item 3 -->
                <div class="animate-on-scroll" data-delay="0.5" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1560343090-f0409e92791a?auto=format&fit=crop&q=80&w=800" alt="Shoe Store Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Step In Style</h4>
                            <p class="text-accent font-medium">Shoe Shop Website</p>
                        </div>
                    </div>
                </div>
                
                <!-- Portfolio Item 4 -->
                <div class="animate-on-scroll" data-delay="0.6" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&q=80&w=800" alt="Startup Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Innovate Labs</h4>
                            <p class="text-accent font-medium">Tech Startup Website</p>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Item 5 -->
                <div class="animate-on-scroll" data-delay="0.7" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1528605248644-14dd04022da1?auto=format&fit=crop&q=80&w=800" alt="Personal Brand Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Alex Morgan</h4>
                            <p class="text-accent font-medium">Personal Brand Website</p>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Item 6 -->
                <div class="animate-on-scroll" data-delay="0.8" data-direction="up">
                    <div class="h-80 rounded-xl overflow-hidden portfolio-item">
                        <img src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?auto=format&fit=crop&q=80&w=800" alt="Artisan Website" class="w-full h-full object-cover">
                        <div class="portfolio-content text-white">
                            <h4 class="text-xl font-bold portfolio-title">Handcrafted Goods</h4>
                            <p class="text-accent font-medium">Artisan Marketplace</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Parallax Section -->
    <div class="parallax py-32 bg-cover bg-center relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?auto=format&fit=crop&q=80&w=900')">
        <div class="parallax-bg absolute inset-0 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?auto=format&fit=crop&q=80&w=900')"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-dark/80 via-dark/70 to-primary/60"></div>

        <!-- Floating elements for parallax -->
        <div class="parallax-float absolute top-20 left-10 w-20 h-20 bg-accent/20 rounded-full blur-xl"></div>
        <div class="parallax-float absolute bottom-20 right-10 w-32 h-32 bg-primary/20 rounded-full blur-2xl"></div>
        <div class="parallax-float absolute top-1/2 left-1/4 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6 animate-on-scroll" data-delay="0" data-direction="up">
                    Ready for Your AI-Powered Website?
                </h2>
                <p class="text-xl text-white mb-10 max-w-2xl mx-auto animate-on-scroll" data-delay="0.2" data-direction="fade">
                    Transform your online presence with the power of artificial intelligence and expert design.
                </p>
                <a href="#contact" class="bg-primary hover:bg-secondary text-white px-10 py-5 rounded-full font-medium btn-hover inline-block text-lg animate-on-scroll" data-delay="0.4" data-direction="up">
                    Start Your Project
                    <i class="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <div class="order-2 lg:order-1">
                    <h2 class="text-2xl text-primary font-bold mb-2 animate-on-scroll" data-delay="0" data-direction="left">
                        Meet The Founder
                    </h2>
                    <h3 class="text-4xl font-bold mb-6 animate-on-scroll highlight-on-hover" data-delay="0.1" data-direction="left">
                        Passionate About AI & Web Innovation
                    </h3>
                    <p class="text-gray-600 mb-8 animate-on-scroll" data-delay="0.2" data-direction="left">
                        After seeing too many local businesses overpay for mediocre websites, I created AI Web Solutions Agency - where cutting-edge technology meets human craftsmanship to deliver websites that actually work.
                    </p>

                    <div class="mb-8 animate-on-scroll" data-delay="0.3" data-direction="left">
                        <h4 class="text-xl font-bold mb-4 highlight-on-hover">Our Vision</h4>
                        <p class="text-gray-600">
                            To democratize exceptional web design - giving every business owner, regardless of budget, access to beautiful, high-performing websites that attract customers and grow their business. Because in today's digital world, your website isn't just your storefront - it's your most powerful employee.
                        </p>
                    </div>

                    <div class="flex flex-wrap gap-4 animate-on-scroll" data-delay="0.4" data-direction="left">
                        <div class="px-3 py-1 bg-primary/10 text-primary rounded-full transition-all duration-300 hover:bg-primary hover:text-white">
                            <i class="fas fa-certificate mr-2"></i> Web Development Expert
                        </div>
                        <div class="px-3 py-1 bg-primary/10 text-primary rounded-full transition-all duration-300 hover:bg-primary hover:text-white">
                            <i class="fas fa-robot mr-2"></i> AI Specialist
                        </div>
                        <div class="px-3 py-1 bg-primary/10 text-primary rounded-full transition-all duration-300 hover:bg-primary hover:text-white">
                            <i class="fas fa-chart-line mr-2"></i> SEO Strategist
                        </div>
                    </div>
                </div>

                <div class="order-1 lg:order-2 animate-on-scroll" data-delay="0" data-direction="right">
                    <div class="relative">
                        <div class="absolute -top-6 -right-6 w-64 h-64 bg-primary/10 rounded-full opacity-20 blur-3xl z-0"></div>
                        <div class="relative">
                            <div class="rounded-2xl overflow-hidden shadow-xl w-full aspect-square object-cover">
                                <img src="founderImage.png" alt="Founder" class="w-full h-full object-cover">
                            </div>
                            <div class="absolute -bottom-6 -left-6 w-40 h-40 rounded-xl bg-white shadow-xl flex items-center justify-center p-6 z-10">
                                <div class="text-5xl font-bold text-primary">5+</div>
                                <div class="text-gray-600 font-medium">Years Experience</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 section-pattern">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-16">
                    <h2 class="text-2xl text-primary font-bold mb-2 animate-on-scroll" data-delay="0">
                        Get In Touch
                    </h2>
                    <h3 class="text-4xl font-bold mb-6 animate-on-scroll" data-delay="0.1">
                        Let's Build Something Amazing
                    </h3>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto animate-on-scroll" data-delay="0.2">
                        Ready to transform your online presence? We'd love to discuss how we can help.
                    </p>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2">
                        <div class="p-10">
                            <form class="space-y-6">
                                <div class="form-field">
                                    <label for="name" class="block text-gray-700 font-medium mb-2">Name</label>
                                    <input type="text" id="name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition duration-300 placeholder-gray-400" placeholder="Your name">
                                </div>
                                <div class="form-field">
                                    <label for="email" class="block text-gray-700 font-medium mb-2">Email</label>
                                    <input type="email" id="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition duration-300" placeholder="<EMAIL>">
                                </div>
                                <div class="form-field">
                                    <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                                    <textarea id="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none transition duration-300" placeholder="Tell us about your project..."></textarea>
                                </div>
                                <button type="submit" class="w-full bg-primary hover:bg-secondary text-white px-6 py-4 rounded-full transition-colors duration-300 font-medium btn-hover">
                                    Send Message
                                </button>
                                <p class="text-gray-600 text-sm text-center mt-4">
                                    By submitting, you agree to our <a href="#" class="text-primary underline">Privacy Policy</a>
                                </p>
                            </form>
                        </div>
                        <div class="bg-gradient-to-br from-primary to-secondary p-10 flex flex-col justify-center text-white">
                            <h3 class="text-2xl font-bold mb-6">Contact Information</h3>
                            <div class="space-y-6 mb-8">
                                <div class="flex items-start">
                                    <div class="mt-1 mr-4">
                                        <i class="fas fa-envelope text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">Email Us</h4>
                                        <p><EMAIL></p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="mt-1 mr-4">
                                        <i class="fas fa-map-marker-alt text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">Serving</h4>
                                        <p>Local Businesses Worldwide</p>
                                    </div>
                                </div>
                            </div>
                            <div class="pt-6 border-t border-white/20">
                                <h4 class="font-semibold mb-4">Office Hours</h4>
                                <p>Monday-Friday: 9am - 6pm</p>
                                <p>Weekends: By Appointment</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-gray-300 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
                <div>
                    <div class="flex items-center mb-6">
                        <div class="bg-primary w-10 h-10 rounded-full flex items-center justify-center">
                            <svg viewBox="0 0 24 24" class="w-6 h-6 text-white">
                                <path fill="currentColor" d="M12 2L3 7l9 5 9-5-9-5zm0 14L3 11l9 5 9-5-9-5z"/>
                                <path fill="currentColor" d="M3 11l9 5 9-5" opacity="0.5"/>
                            </svg>
                        </div>
                        <span class="ml-3 text-xl font-bold text-white">Synthetix</span>
                    </div>
                    <p class="mb-4 text-[#666666]">
                        AI-powered web development solutions for businesses that want to thrive online.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="social-icon text-gray-400">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-icon text-gray-400">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="social-icon text-gray-400">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-icon text-gray-400">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-white font-bold text-lg mb-6">Services</h4>
                    <ul class="space-y-3">
                        <li><a href="#services" class="hover:text-white transition-colors duration-300">AI Website Design</a></li>
                        <li><a href="#services" class="hover:text-white transition-colors duration-300">Development</a></li>
                        <li><a href="#services" class="hover:text-white transition-colors duration-300">SEO Optimization</a></li>
                        <li><a href="#services" class="hover:text-white transition-colors duration-300">Ongoing Support</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-bold text-lg mb-6">Company</h4>
                    <ul class="space-y-3">
                        <li><a href="#about" class="hover:text-white transition-colors duration-300">About Us</a></li>
                        <li><a href="#portfolio" class="hover:text-white transition-colors duration-300">Portfolio</a></li>
                        <li><a href="#contact" class="hover:text-white transition-colors duration-300">Contact</a></li>
                        <li><a href="#" class="hover:text-white transition-colors duration-300">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-white font-bold text-lg mb-6">Newsletter</h4>
                    <p class="mb-4">Stay updated with AI web trends and special offers.</p>
                    <form class="flex newsletter-form">
                        <input type="email" placeholder="Your email" class="px-4 py-3 bg-gray-800 rounded-l-lg focus:outline-none w-full transition-all duration-300">
                        <button class="bg-primary hover:bg-secondary px-4 py-3 rounded-r-lg transition-all duration-300 btn-hover">
                            <i class="fas fa-paper-plane text-white"></i>
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-16 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p>&copy; 2023 AI Web Solutions Agency. All rights reserved.</p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="hover:text-white transition-colors duration-300">Terms</a>
                    <a href="#" class="hover:text-white transition-colors duration-300">Privacy</a>
                    <a href="#" class="hover:text-white transition-colors duration-300">Cookies</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileCloseButton = document.getElementById('mobile-close-button');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        });
        
        mobileCloseButton.addEventListener('click', () => {
            mobileMenu.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });
        
        // Initialize animations and parallax on load
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.animate-on-scroll');

            // First set all elements to animate-out state
            animateElements.forEach(element => {
                element.classList.add('animate-out');
            });

            // Enhanced intersection observer with better timing
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.remove('animate-out');
                    } else {
                        // Optional: re-hide elements when they scroll out of view
                        // entry.target.classList.add('animate-out');
                    }
                });
            }, {
                threshold: 0.15,
                rootMargin: '0px 0px -50px 0px'
            });

            animateElements.forEach(element => {
                observer.observe(element);

                // If element is already in view, animate it immediately
                const rect = element.getBoundingClientRect();
                if (rect.top < window.innerHeight * 0.8) {
                    element.classList.remove('animate-out');
                }
            });

            // Enhanced Parallax Scrolling Effects
            const parallaxElements = document.querySelectorAll('.parallax-bg');
            const parallaxFloats = document.querySelectorAll('.parallax-float');

            function updateParallax() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                const rateFloat = scrolled * -0.3;

                parallaxElements.forEach(element => {
                    element.style.transform = `translateY(${rate}px)`;
                });

                parallaxFloats.forEach((element, index) => {
                    const speed = 0.2 + (index * 0.1);
                    element.style.transform = `translateY(${scrolled * speed}px)`;
                });
            }

            // Throttled scroll event for better performance
            let ticking = false;
            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                    setTimeout(() => { ticking = false; }, 16);
                }
            }

            window.addEventListener('scroll', requestTick);

            // Also check on window resize
            window.addEventListener('resize', function() {
                animateElements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    if (rect.top < window.innerHeight * 0.8) {
                        element.classList.remove('animate-out');
                    }
                });
            });
        });
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                    }
                }
            });
        });
    </script>
<p style="border-radius: 8px; text-align: center; font-size: 12px; color: #fff; margin-top: 16px;position: fixed; left: 8px; bottom: 8px; z-index: 10; background: rgba(0, 0, 0, 0.8); padding: 4px 8px;">Made with <img src="https://enzostvs-deepsite.hf.space/logo.svg" alt="DeepSite Logo" style="width: 16px; height: 16px; vertical-align: middle;display:inline-block;margin-right:3px;filter:brightness(0) invert(1);"><a href="https://enzostvs-deepsite.hf.space" style="color: #fff;text-decoration: underline;" target="_blank" >DeepSite</a> - 🧬 <a href="https://enzostvs-deepsite.hf.space?remix=AAJ007/agency" style="color: #fff;text-decoration: underline;" target="_blank" >Remix</a></p></body>
</html>