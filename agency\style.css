@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto:wght@400;500;700&display=swap');

:root {
    --primary-font: 'Inter', sans-serif;
    --secondary-font: '<PERSON>o', sans-serif;
    --charcoal-grey: #333333;
    --medium-grey: #666666;
    --white: #FFFFFF;
}

body {
    padding: 2rem;
    font-family: var(--secondary-font);
    color: var(--charcoal-grey);
    line-height: 1.6;
}

h1 {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 3rem; /* Approximately 48px */
    color: var(--charcoal-grey);
    margin-top: 0;
    line-height: 1.2;
}

h2 {
    font-family: var(--primary-font);
    font-weight: 700;
    font-size: 2.25rem; /* Approximately 36px */
    color: var(--charcoal-grey);
    line-height: 1.3;
}

h3 {
    font-family: var(--primary-font);
    font-weight: 600;
    font-size: 1.5rem; /* Approximately 24px */
    color: var(--charcoal-grey);
    line-height: 1.4;
}

p {
    font-family: var(--secondary-font);
    color: var(--medium-grey);
    font-size: 1.125rem; /* Approximately 18px */
    margin-bottom: 1rem;
    margin-top: 0.5rem;
    line-height: 1.7;
}

small, .small-text {
    font-family: var(--secondary-font);
    font-size: 0.875rem; /* Approximately 14px */
    color: var(--medium-grey);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem; /* Scale down H1 for tablets */
    }
    h2 {
        font-size: 1.75rem;
    }
    h3 {
        font-size: 1.25rem;
    }
    p {
        font-size: 1rem; /* Scale down body text for mobile */
    }
}

/* Placeholder for navigation links and CTA buttons - assuming they will be styled as classes */
.nav-link {
    font-family: var(--primary-font);
    font-size: 1.125rem; /* 18px */
    font-weight: 500;
    color: var(--charcoal-grey);
}

.cta-button {
    font-family: var(--primary-font);
    font-size: 1.25rem; /* 20px */
    font-weight: 600;
    /* Example accent color, replace with actual brand accent */
    background-color: #007bff; 
    color: var(--white);
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    display: inline-block;
}

/* Ensure contrast for dark backgrounds if any */
.dark-background-section {
    background-color: var(--charcoal-grey);
    color: var(--white);
}

.dark-background-section h1, 
.dark-background-section h2, 
.dark-background-section h3, 
.dark-background-section p, 
.dark-background-section .small-text, 
.dark-background-section .nav-link, 
.dark-background-section .cta-button {
    color: var(--white);
}

/* Existing styles */
.card {
    max-width: 620px;
    margin: 0 auto;
    padding: 16px;
    border: 1px solid lightgray;
    border-radius: 16px;
}

.card p:last-child {
    margin-bottom: 0;
}

h1 {
	font-size: 16px;
	margin-top: 0;
}

p {
	color: rgb(107, 114, 128);
	font-size: 15px;
	margin-bottom: 10px;
	margin-top: 5px;
}

.card {
	max-width: 620px;
	margin: 0 auto;
	padding: 16px;
	border: 1px solid lightgray;
	border-radius: 16px;
}

.card p:last-child {
	margin-bottom: 0;
}
